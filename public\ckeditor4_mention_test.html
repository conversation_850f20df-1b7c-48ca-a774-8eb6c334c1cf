<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CKEditor 4 Mention 功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .info {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .editor-container {
            margin: 20px 0;
        }
        .test-buttons {
            margin: 20px 0;
            text-align: center;
        }
        .test-buttons button {
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-buttons button:hover {
            background: #45a049;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            background: #f0f0f0;
            border: 1px solid #ddd;
        }

        /* Mention 下拉列表样式 */
        .cke_autocomplete_panel {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-height: 200px;
            overflow-y: auto;
            z-index: 10000;
        }

        .cke_autocomplete_panel ul {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .cke_autocomplete_panel li {
            padding: 0;
            margin: 0;
            cursor: pointer;
        }

        .cke_autocomplete_panel li:hover,
        .cke_autocomplete_panel li.cke_autocomplete_selected {
            background-color: #f0f0f0;
        }

        .mention-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            gap: 8px;
        }

        .mention-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            object-fit: cover;
        }

        .mention-name {
            font-weight: bold;
            color: #333;
        }

        .mention-username {
            color: #666;
            font-size: 0.9em;
        }

        /* 编辑器内的 mention 样式 */
        .mention {
            background-color: #e3f2fd;
            color: #1976d2;
            padding: 2px 4px;
            border-radius: 3px;
            text-decoration: none;
            font-weight: bold;
        }

        .mention:hover {
            background-color: #bbdefb;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 CKEditor 4 Mention 功能测试</h1>

        <div class="info">
            <h3>📝 使用说明</h3>
            <ul>
                <li>在编辑器中输入 <strong>@</strong> 符号开始提及用户</li>
                <li>输入用户名的一部分，系统会自动搜索匹配的用户</li>
                <li>使用方向键选择用户，按回车确认</li>
                <li>提及的用户会以蓝色高亮显示</li>
            </ul>
        </div>

        <div class="editor-container">
            <h3>测试编辑器</h3>
            <textarea id="mention-editor" name="content">
                <h2>🎯 Mention 功能测试</h2>
                <p>请在下面尝试输入 @ 符号来提及用户：</p>
                <p>例如：输入 @admin 或 @user 来搜索用户</p>
                <p></p>
            </textarea>
        </div>

        <div class="test-buttons">
            <button onclick="getContent()">获取内容</button>
            <button onclick="getMentions()">获取提及用户</button>
            <button onclick="testAPI()">测试API</button>
        </div>

        <div id="test-result" class="result" style="display: none;">
            测试结果将显示在这里...
        </div>
    </div>

    <!-- CKEditor 4.25.1 - LTS版本 -->
    <script src="https://cdn.ckeditor.com/4.25.1/full/ckeditor.js"></script>
    <script src="/static/ckeditor4/ckeditor4-fixed.js?v=1.3"></script>

    <script>
        let editor;

        // 等待CKEditor加载完成
        CKEDITOR.on('loaded', function() {
            console.log('CKEditor 已加载，版本:', CKEDITOR.version);

            // 初始化编辑器
            editor = CKEDITOR.replace('mention-editor', {
                language: 'zh-cn',
                height: 400,
                extraPlugins: 'autocomplete,textmatch,mentions',
                toolbar: [
                    { name: 'clipboard', items: ['Undo', 'Redo'] },
                    { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline'] },
                    { name: 'paragraph', items: ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent'] },
                    { name: 'links', items: ['Link', 'Unlink'] },
                    { name: 'insert', items: ['Image', 'Table', 'SpecialChar'] },
                    { name: 'styles', items: ['Format', 'Font', 'FontSize'] },
                    { name: 'colors', items: ['TextColor', 'BGColor'] }
                ],
                // Mention 配置
                mentions: [
                    {
                        feed: getMentionsFeed,
                        itemTemplate: '<li data-id="{id}"><div class="mention-item"><img class="mention-avatar" src="{avatar}" /><strong class="mention-name">{name}</strong><span class="mention-username">@{username}</span></div></li>',
                        outputTemplate: '<a href="/user/{username}" class="mention" data-mention-id="{id}">@{name}</a>',
                        minChars: 1,
                        marker: '@'
                    }
                ]
            });

            // 编辑器准备就绪后的处理
            editor.on('instanceReady', function() {
                console.log('编辑器已准备就绪');
                showResult('✅ 编辑器初始化成功', `CKEditor ${CKEDITOR.version} 已加载，Mention 功能已启用`);
            });

            // 监听 mention 事件
            editor.on('change', function() {
                console.log('编辑器内容已更改');
            });
        });

        // Mention 数据获取函数
        function getMentionsFeed(opts, callback) {
            const query = opts.query.toLowerCase();
            console.log('搜索用户:', query);

            // 发送AJAX请求获取用户数据
            fetch('/user/searchUsers', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `query=${encodeURIComponent(query)}`
            })
            .then(response => response.json())
            .then(data => {
                console.log('API响应:', data);
                if (data.code === 1 && data.data) {
                    const users = data.data.map(user => ({
                        id: user.id,
                        name: user.name,
                        username: user.username,
                        avatar: user.avatar
                    }));
                    console.log('格式化用户数据:', users);
                    callback(users);
                } else {
                    console.log('没有找到用户');
                    callback([]);
                }
            })
            .catch(error => {
                console.error('Mention search error:', error);
                callback([]);
            });
        }

        function getContent() {
            if (editor) {
                const content = editor.getData();
                showResult('📄 编辑器内容', `<pre>${escapeHtml(content)}</pre>`);
                console.log('编辑器内容：', content);
            } else {
                showResult('❌ 错误', '编辑器未初始化');
            }
        }

        function getMentions() {
            if (editor) {
                const mentionedUsers = getMentionedUsers(editor);
                showResult('👥 提及的用户', `找到 ${mentionedUsers.length} 个被提及的用户：<br>${JSON.stringify(mentionedUsers, null, 2)}`);
                console.log('被提及的用户：', mentionedUsers);
            } else {
                showResult('❌ 错误', '编辑器未初始化');
            }
        }

        function testAPI() {
            fetch('/user/searchUsers', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: 'query=admin'
            })
            .then(response => response.json())
            .then(data => {
                showResult('🔍 API测试结果', `<pre>${JSON.stringify(data, null, 2)}</pre>`);
                console.log('API测试结果：', data);
            })
            .catch(error => {
                showResult('❌ API测试失败', error.message);
                console.error('API测试错误：', error);
            });
        }

        function showResult(title, message) {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = `<h4>${title}</h4><div>${message}</div>`;
            resultDiv.style.display = 'block';
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
</body>
</html>
