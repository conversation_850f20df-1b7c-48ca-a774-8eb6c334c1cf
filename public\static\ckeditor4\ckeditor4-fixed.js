/**
 * CKEditor 4 修复版本 - 确保与最新版本兼容
 */

// 等待CKEditor加载完成
function waitForCKEditor(callback) {
    if (typeof CKEDITOR !== 'undefined') {
        callback();
    } else {
        setTimeout(() => waitForCKEditor(callback), 100);
    }
}

// 获取基础配置
function getBaseConfig() {
    return {
        language: 'zh-cn',
        allowedContent: true,
        autoParagraph: false,
        removePlugins: 'elementspath',
        filebrowserUploadUrl: '/admin/editorImage',
        filebrowserImageUploadUrl: '/admin/editorImage',
        // 禁用版本检查和安全警告
        versionCheck: false,
        // 禁用通知
        notification_hideOnClick: true
    };
}

// 获取管理员配置
function getAdminConfig() {
    const baseConfig = getBaseConfig();
    return Object.assign({}, baseConfig, {
        height: 400,
        extraPlugins: 'uploadimage,colorbutton,font,justify,specialchar',
        toolbar: [
            { name: 'document', items: ['Source'] },
            { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText', 'PasteFromWord', '-', 'Undo', 'Redo'] },
            '/',
            { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', '-', 'RemoveFormat'] },
            { name: 'paragraph', items: ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', '-', 'Blockquote'] },
            { name: 'links', items: ['Link', 'Unlink'] },
            '/',
            { name: 'styles', items: ['Format', 'Font', 'FontSize'] },
            { name: 'colors', items: ['TextColor', 'BGColor'] },
            { name: 'insert', items: ['Image', 'Table', 'HorizontalRule', 'SpecialChar'] },
            { name: 'tools', items: ['Maximize'] }
        ],
        font_names: 'Arial/Arial, Helvetica, sans-serif;Times New Roman/Times New Roman, Times, serif;微软雅黑/Microsoft YaHei;宋体/SimSun;黑体/SimHei',
        fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;24/24px;28/28px;36/36px'
    });
}

// 获取前台配置
function getHomeConfig() {
    const baseConfig = getBaseConfig();
    return Object.assign({}, baseConfig, {
        height: 300,
        extraPlugins: 'uploadimage,colorbutton,font,specialchar,ajax,textmatch,autocomplete,mentions',
        toolbar: [
            { name: 'clipboard', items: ['Undo', 'Redo'] },
            { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline'] },
            { name: 'paragraph', items: ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent'] },
            { name: 'links', items: ['Link', 'Unlink'] },
            { name: 'insert', items: ['Image', 'Table', 'SpecialChar'] },
            { name: 'styles', items: ['Format', 'Font', 'FontSize'] },
            { name: 'colors', items: ['TextColor', 'BGColor'] }
        ],
        font_names: 'Arial/Arial, Helvetica, sans-serif;微软雅黑/Microsoft YaHei;宋体/SimSun',
        fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;24/24px',
        // Mention 配置
        mentions: [
            {
                feed: getMentionsFeed,
                itemTemplate: '<li data-id="{id}"><div class="mention-item"><img class="mention-avatar" src="{avatar}" /><strong class="mention-name">{name}</strong><span class="mention-username">@{username}</span></div></li>',
                outputTemplate: '<a href="/user/{username}" class="mention" data-mention-id="{id}">{name}</a>',
                minChars: 1,
                marker: '@'
            }
        ]
    });
}

// CKEditor 4 管理器类
class CKEditor4Manager {
    constructor() {
        this.editors = [];
    }

    async initAll(selector) {
        waitForCKEditor(() => {
            const elements = document.querySelectorAll(selector);

            for (const element of elements) {
                try {
                    // 为每个元素创建唯一ID
                    if (!element.id) {
                        element.id = 'ckeditor4_' + Math.random().toString(36).substring(2, 9);
                    }

                    // 根据页面类型选择配置
                    const config = window.location.pathname.includes('/admin/') ? getAdminConfig() : getHomeConfig();
                    const editor = CKEDITOR.replace(element.id, config);

                    // 编辑器准备就绪后的处理
                    editor.on('instanceReady', (evt) => {
                        const editor = evt.editor;

                        // 设置最小高度
                        editor.ui.space('contents').setStyle('min-height', '300px');

                        // 查找字数统计容器
                        const container = element.closest('.editor-container');
                        const wordCountContainer = container ?
                            container.querySelector('.editor_container__word-count') : null;

                        if (wordCountContainer) {
                            // 创建字数统计显示
                            const wordCountDiv = document.createElement('div');
                            wordCountDiv.style.cssText = 'padding: 5px; font-size: 12px; color: #666; border-top: 1px solid #ddd;';
                            wordCountContainer.appendChild(wordCountDiv);

                            // 更新字数统计
                            const updateWordCount = () => {
                                const text = editor.getData().replace(/<[^>]*>/g, '');
                                const words = text.trim() ? text.trim().split(/\s+/).length : 0;
                                const chars = text.length;
                                wordCountDiv.textContent = `字数: ${words} | 字符: ${chars}`;
                            };

                            // 监听内容变化
                            editor.on('change', updateWordCount);
                            editor.on('key', updateWordCount);

                            // 初始更新
                            updateWordCount();
                        }
                    });

                    this.editors.push(editor);
                } catch (error) {
                    console.error('Error initializing CKEditor 4:', error);
                }
            }
        });
    }

    // 获取编辑器实例
    getEditor(index = 0) {
        return this.editors[index];
    }

    // 获取所有编辑器
    getAllEditors() {
        return this.editors;
    }

    // 销毁所有编辑器
    destroyAll() {
        this.editors.forEach(editor => {
            if (editor && editor.destroy) {
                editor.destroy();
            }
        });
        this.editors = [];
    }
}

// Mention 数据获取函数
function getMentionsFeed(opts, callback) {
    const query = opts.query.toLowerCase();

    // 发送AJAX请求获取用户数据
    fetch('/user/searchUsers', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: `query=${encodeURIComponent(query)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1 && data.data) {
            const users = data.data.map(user => {
                let name = user.nickname || user.username;
                // 确保name不以@开头，因为mentions插件会自动添加marker
                if (name.startsWith('@')) {
                    name = name.substring(1);
                }
                return {
                    id: user.id,
                    name: name,
                    username: user.username,
                    avatar: user.avatar || '/static/home/<USER>/default-avatar.png'
                };
            });
            callback(users);
        } else {
            callback([]);
        }
    })
    .catch(error => {
        console.error('Mention search error:', error);
        callback([]);
    });
}

// 获取提及的用户
function getMentionedUsers(editor) {
    const mentionedUsers = [];

    if (!editor || !editor.getData) {
        return mentionedUsers;
    }

    try {
        const content = editor.getData();

        // 使用正则表达式匹配 mention 链接
        const mentionRegex = /<a[^>]*class="mention"[^>]*data-mention-id="(\d+)"[^>]*>@([^<]+)<\/a>/g;
        let match;

        while ((match = mentionRegex.exec(content)) !== null) {
            const userId = match[1];
            const userName = match[2];

            // 避免重复添加
            if (!mentionedUsers.find(user => user.userId === userId)) {
                mentionedUsers.push({
                    userId: userId,
                    userName: userName
                });
            }
        }

        console.log('找到被提及的用户:', mentionedUsers);
        return mentionedUsers;
    } catch (error) {
        console.error('获取提及用户时出错:', error);
        return mentionedUsers;
    }
}

// 全局可用
window.CKEditor4Manager = CKEditor4Manager;
window.CKEditorManager = CKEditor4Manager; // 保持兼容性
window.getMentionedUsers = getMentionedUsers;

console.log('CKEditor 4 修复版本已加载');
