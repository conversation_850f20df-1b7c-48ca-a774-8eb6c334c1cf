<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mention API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-form {
            margin: 20px 0;
        }
        .test-form input {
            width: 200px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-right: 10px;
        }
        .test-form button {
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-form button:hover {
            background: #45a049;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            background: #f0f0f0;
            border: 1px solid #ddd;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Mention API 测试</h1>

        <div class="test-form">
            <h3>测试用户搜索接口</h3>
            <input type="text" id="searchQuery" placeholder="输入搜索关键词" value="test">
            <button onclick="testAPI()">搜索用户</button>
        </div>

        <div id="result" class="result" style="display: none;">
            测试结果将显示在这里...
        </div>

        <div class="test-form">
            <h3>预设测试</h3>
            <button onclick="testWithQuery('test')">搜索 "test"</button>
            <button onclick="testWithQuery('admin')">搜索 "admin"</button>
            <button onclick="testWithQuery('user')">搜索 "user"</button>
            <button onclick="testWithQuery('')">空搜索</button>
        </div>
    </div>

    <script>
        function testAPI() {
            const query = document.getElementById('searchQuery').value;
            testWithQuery(query);
        }

        function testWithQuery(query) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在搜索...';

            console.log('搜索查询:', query);

            fetch('/user/searchUsers', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `query=${encodeURIComponent(query)}`
            })
            .then(response => {
                console.log('响应状态:', response.status);
                console.log('响应头:', response.headers);
                return response.text();
            })
            .then(text => {
                console.log('原始响应:', text);
                try {
                    const data = JSON.parse(text);
                    console.log('解析后的数据:', data);

                    if (data.code === 1) {
                        resultDiv.className = 'result success';
                        resultDiv.textContent = `✅ 搜索成功！\n\n查询: "${query}"\n找到 ${data.data.length} 个用户:\n\n${JSON.stringify(data.data, null, 2)}`;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.textContent = `❌ 搜索失败\n\n响应: ${JSON.stringify(data, null, 2)}`;
                    }
                } catch (error) {
                    console.error('JSON解析错误:', error);
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ JSON解析错误\n\n原始响应: ${text}\n\n错误: ${error.message}`;
                }
            })
            .catch(error => {
                console.error('请求错误:', error);
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败\n\n错误: ${error.message}`;
            });
        }

        // 页面加载时自动测试
        window.onload = function() {
            testWithQuery('test');
        };
    </script>
</body>
</html>
