<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <title>Post - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css?v=1.1" rel="stylesheet" />


</head>
<body>
    <section
        class="pb-5 md:pb-20 relative bg-[url(__IMG__/bg.jpg)] ">
        <span class="absolute top-0 left-0 w-full h-full bg-[url(__IMG__/tm-left.png)] bg-no-repeat bg-[top_left] z-0 bg-size-[10rem] md:bg-size-[26.9375rem]"></span>
        <span class="absolute top-0 right-0 w-full h-full bg-[url(__IMG__/tm-right.png)] bg-no-repeat bg-[top_right] z-0 bg-size-[10rem] md:bg-size-[25.0625rem]"></span>

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12 relative z-10">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        Search
                    </li>
                </ul>
            </div>
            <!-- data-aos="fade-up" -->
            <div class="contact" data-aos="fade-up">
                <h1 class="text-2xl md:text-5xl md:mb-10 mb-5 Roboto_Bold"> Start here</h1>
                <div class="bg-[#f8fdff] rounded-xl border border-[#dae9ff] p-4 md:px-7 md:py-10">
                    <form action="/iCommunity/post" id="formId">
                        <!-- 类型选择 -->
                        <div class="mb-5 md:mb-10">
                            <h2 class="text-xl font-bold mb-2.5 md:text-xl md:mb-6">
                                What would you like to do...
                            </h2>
                            <div class="flex flex-col gap-y-3.5 md:flex-row md:gap-x-10 md:gap-y-0 md:w-10/12 md:grid md:grid-cols-2">
                                <label class="grid grid-cols-[24px_1fr_auto] items-center gap-6 rounded-lg p-4 ring-1 ring-transparent hover:bg-gray-100 has-checked:bg-indigo-50 has-checked:text-[#155797] has-checked:font-bold has-checked:ring-indigo-200 dark:hover:bg-white/5 dark:has-checked:bg-indigo-950 dark:has-checked:text-indigo-200 dark:has-checked: text-[#111111] has-checked:border-[#155290] border border-[#c4d7ff] text-center cursor-pointer md:text-3xl md:px-6"
                                    for="question">
                                    <svg class="w-4 md:w-8" fill="currentColor" viewBox="0 0 1024 1024">
                                        <path
                                            d="M511.998 16.512c-273.649 0-495.482 221.837-495.482 495.486 0 273.651 221.833 495.49 495.482 495.49 273.653 0 495.486-221.839 495.486-495.488 0-273.649-221.831-495.488-495.486-495.488zM560.084 797.914c0 13.682-11.089 24.776-24.774 24.776h-74.323c-13.679 0-24.772-11.093-24.772-24.776v-74.321c0-13.686 11.093-24.778 24.772-24.778h74.323c13.684 0 24.774 11.091 24.774 24.778v74.321zM682.891 456.897c-9.958 14.199-32.561 32.291-60.858 54.35l-31.359 21.64c-15.23 11.814-28.738 25.568-33.733 41.315-1.707 5.365-2.986 14.183-3.847 23.706-0.434 4.792-4.721 14.568-14.741 14.568-24.551 0-71.341 0-80.651 0-13.109 0-15.451-10.268-15.232-15.291 1.451-32.919 4.468-62.144 17.88-77.878 27.155-31.839 88.943-71.469 88.943-71.469 9.407-7.099 17.023-14.816 22.783-23.226 10.471-14.438 19.158-30.294 19.158-47.626 0-19.921-5.824-38.079-17.51-54.515-11.646-16.371-32.979-24.573-63.891-24.573-30.43 0-52.001 10.1-64.716 30.291-9.393 14.918-15.307 28.634-17.756 43.558-0.871 5.282-4.258 16.407-15.548 16.407-23.854 0-67.833 0-78.66 0-16.749 0-20.437-10.854-19.953-16.086 6.063-65.94 31.831-110.993 77.393-139.922 30.981-19.918 69.097-29.913 114.31-29.913 59.41 0 108.726 14.162 148.043 42.527 39.247 28.326 58.927 70.299 58.927 125.952 0.004 34.082-11.958 62.822-28.98 86.185z"
                                            p-id="4486"></path>
                                    </svg>
                                    Ask a Question
                                    <input id="question" type="radio" class="box-content h-1.5 w-1.5 appearance-none rounded-full border-[0.3125rem] border-white bg-white bg-clip-padding ring-1 ring-gray-950/20 outline-none checked:border-[#155797] checked:ring-[#155797] md:w-3 md:h-3"
                                        name="post_type" checked="" value="0">
                                </label>

                                <label for="posting" class="grid grid-cols-[24px_1fr_auto] items-center gap-6 rounded-lg p-4 ring-1 ring-transparent hover:bg-gray-100 has-checked:bg-indigo-50 has-checked:text-[#155797] has-checked:font-bold has-checked:ring-indigo-200 dark:hover:bg-white/5 dark:has-checked:bg-indigo-950 dark:has-checked:text-indigo-200 dark:has-checked: text-[#111111] has-checked:border-[#155290] border border-[#c4d7ff] text-center cursor-pointer md:text-3xl md:px-6">
                                    <svg t="1751437602379" fill="currentColor" class="w-4 md:w-8"
                                        viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
                                        p-id="6249" >
                                        <path
                                            d="M504.149333 89.429333A422.570667 422.570667 0 0 0 81.578667 512c0 66.56 2.048 192.170667 4.096 289.109333a136.533333 136.533333 0 0 0 136.533333 133.461334h267.946667a431.445333 431.445333 0 0 0 436.565333-414.72A422.570667 422.570667 0 0 0 504.149333 89.429333z m30.037334 580.266667H286.72a34.133333 34.133333 0 1 1 0-68.266667h247.466667a34.133333 34.133333 0 0 1 0 68.266667zM662.869333 443.733333H286.72a34.133333 34.133333 0 1 1 0-68.266666h375.466667a34.133333 34.133333 0 0 1 0 68.266666z"
                                            p-id="6250"></path>
                                    </svg>
                                    Start posting
                                    <input id="posting" type="radio"
                                        class="box-content h-1.5 w-1.5 appearance-none rounded-full border-[0.3125rem] border-white bg-white bg-clip-padding ring-1 ring-gray-950/20 outline-none checked:border-[#155797] checked:ring-[#155797] md:w-3 md:h-3"
                                        name="post_type" value="1">
                                </label>
                            </div>
                        </div>

                        <div class="mb-5 md:mb-10">
                            <h2 class="text-lg font-bold mb-2.5 md:text-xl md:mb-6">
                                Title
                            </h2>
                            <div class="">
                                <input id="post_title" name="title" type="text" placeholder="Enter title here" value="" class="border w-full py-2.5 px-3 border-[#bdcbe9] bg-white rounded-md h-[3.125rem] md:h-[5rem] md:rounded-xl md:px-[2.1875rem] md:text-xl" />
                            </div>
                            <div class="hidden error text-[#ff0000] mt-2
                            md:text-base
                            ">
                                Please fill in the information.
                            </div>
                        </div>
                        <div class="mb-5 md:mb-10">
                            <h2 class="text-lg font-bold mb-2.5 md:text-xl md:mb-6">Description</h2>
                            <div class="md:text-xl">
                                <textarea name="content" placeholder="Enter description here" class="tiny-editor border w-full py-2.5 px-3 border-[#bdcbe9] bg-white rounded-md h-[10rem]" id="textarea"></textarea>
                            </div>
                        </div>

                        <div class="mb-5 md:mb-10">
                            <h2 class="text-lg font-bold mb-2.5 md:text-xl md:mb-6">Topic</h2>
                            <div class="relative flex flex-col mt-2 z-10 bg-white md:text-xl">
                                <select name="topic" id="Country" class="country w-full h-[3.125rem] border border-[#bdcbe9] rounded-md bg-transparent px-5 pr-12 appearance-none text-[#999] cursor-pointer
                                    md:h-[5rem]
                                    ">
                                    <option value="" disabled selected style="color:#999;">Choose your Topic</option>
                                    {volist name="topic" id="vo"}
                                    <option value="{$vo.name}" style="color: #333;">{$vo.name}</option>
                                    {/volist}
                                </select>
                                <button type="button" class="rounded-md top-1/2 -translate-y-1/2 absolute right-2 w-10 h-full -z-10 flex items-center justify-center">
                                    <svg t="1751443158415" class="w-4 md:w-6" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1472"><path d="M483.072 714.496l30.165333 30.208 415.957334-415.829333a42.837333 42.837333 0 0 0 0-60.288 42.538667 42.538667 0 0 0-60.330667-0.042667l-355.541333 355.413333-355.242667-355.413333a42.496 42.496 0 0 0-60.288 0 42.837333 42.837333 0 0 0-0.085333 60.330667l383.701333 383.872 1.706667 1.749333z" fill="#999999" p-id="1473"></path></svg>
                                </button>
                            </div>
                            <div class=" hidden error text-[#ff0000] mt-2 md:text-base">
                                Please fill in the information.
                            </div>
                        </div>

                        <div class="mb-5 md:mb-10">
                            <h2 class="text-lg font-bold mb-2.5 md:text-xl md:mb-6">
                              Tags <span class="text-[#999999]">Optional</span>
                            </h2>
                            <div class="relative flex flex-col mt-2 z-10 bg-white md:text-xl">
                                <input type="hidden" name="tags" id="tags-input" value="">
                                <div id="multiselect"></div>
                            </div>
                        </div>

                        <input type="hidden" name="mentioned_users" id="mentioned-users" value="">

                        <div class="">
                            <button type="submit" id="submitBtn" class="bg-[#155797] text-white text-lg w-full h-[3.125rem] rounded-md md:h-[5rem] md:w-[14.375rem] cursor-pointer">CREATE</button>
                        </div>
                    </form>
                </div>

            </div>
        </div>
    </section>

    {include file="public:footer"}

    {include file="public:foot"}

    <!-- CKEditor 4 免费版本 - 使用 standard-all 版本以支持 autocomplete 和 mentions 插件 -->
    <script src="https://cdn.ckeditor.com/4.22.1/standard-all/ckeditor.js"></script>
    <script src="__STATIC__/ckeditor4/ckeditor4-fixed.js?v=1.7"></script>

    <script>
        var customEditor = new CKEditorManager();
        customEditor.initAll('.tiny-editor');
    </script>

    <script>
        var selects = document.querySelectorAll('select.country');
        selects.forEach(function (select) {
            // 监听 change 事件
            select.addEventListener('change', function () {
                if (select.value === "") {
                    select.style.color = "#999";
                } else {
                    select.style.color = "#333";
                }
            });
            // 初始化时设置颜色
            if (select.value === "") {
                select.style.color = "#999";
            } else {
                select.style.color = "#333";
            }
        });

        const options = {$tagsOptions|raw};
        const multi = new MultiSelect({
            container: '#multiselect',
            options,
            max: 8, // 最多能选n个
            defaultSelected: [] // 这里设置默认选中
        });

        // 覆盖原方法，直接更新 input
        multi.renderTags = function() {
            MultiSelect.prototype.renderTags.call(this); // 调用父类方法
            MultiSelect.prototype.renderDropdown.call(this); // 调用父类方法
            document.getElementById('tags-input').value = this.selected.join(',');
        };
    </script>

    <script>
        // 获取表单和提交按钮
        const $form = $('#formId');
        const $submitBtn = $('#submitBtn');
        // 初始绑定提交事件
        if ($form.length) {
            $form.on('submit', handleSubmit);
        }
        // 提交处理函数
        async function handleSubmit(event) {
            event.preventDefault(); // 阻止默认表单提交

            // 确保所有编辑器内容已同步到 textarea
            customEditor.editors.forEach((editor, index) => {
                editor.updateSourceElement();

                // 提取提及的用户
                const mentionedUsers = getMentionedUsers(editor);
                console.log('被提及的用户:', mentionedUsers);

                // 可以将这些用户ID添加到表单数据中
                const userIds = mentionedUsers.map(user => user.userId).join(',');
                document.getElementById('formId').querySelector('[name="mentioned_users"]').value = userIds;
            });

            // 解绑提交事件（避免重复提交）
            $form.off('submit', handleSubmit);
            // 禁用提交按钮（防止重复点击）
			const originalBtnText = $submitBtn.text();
            $submitBtn.prop('disabled', true).text('Submitting...');
            try {
                const formData = new FormData($form[0]);
                const response = await fetch($form.attr('action'), {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                });
                const data = await response.json();
                console.log(data)
                if (data.code === 1) {
                    // 提交成功
                    layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                        location.reload();
                    });
                } else {
                    // 提交失败
                    if(data.url && data.url !== '') {
                        layer.msg(data.msg, { icon: 2, time: 2000 }, () => {
                            window.location.href = data.url;
                        });
                    } else {
                        layer.msg(data.msg, { icon: 2 });
                    }
                }
            } catch (error) {
                console.error('Error:', error);
                layer.msg("提交失败，请重试", { icon: 2 });
            } finally {
                // 无论成功或失败，重新绑定事件并恢复按钮状态
                $submitBtn.prop('disabled', false).text(originalBtnText);
                $form.on('submit', handleSubmit);
            }
        }
    </script>

</body>

</html>