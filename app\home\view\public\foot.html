<script src="__JS__/vendors/jquery-1.8.3.min.js"></script>
<script src="__JS__/vendors/swiper-bundle.min.js"></script>
<script src="__JS__/vendors/aos.js"></script>

<script src="__STATIC__/layer/layer.js"></script>
<script src="__JS__/encapsulate.js"></script>
<script src="__JS__/index.js"></script>

<script src="__JS__/TabSwitch.js"></script>
<script src="__JS__/ShowMore.js"></script>
<script src="__JS__/MultiSelect.js"></script>

<script>
    AOS.init(); //延迟加载动画效果
</script>

<!-- CKEditor 全局配置 - 禁用版本检查和安全警告 -->
<script>
    // 在加载CKEditor之前设置配置以禁用版本检查
    window.CKEDITOR_CONFIG = {
        versionCheck: false
    };

    // 重写console.warn以屏蔽CKEditor安全警告
    (function() {
        const originalWarn = console.warn;
        console.warn = function(...args) {
            const message = args.join(' ');
            // 屏蔽CKEditor版本安全警告
            if (message.includes('CKEditor') && (message.includes('not secure') || message.includes('version is not secure'))) {
                return;
            }
            originalWarn.apply(console, args);
        };
    })();
</script>

<script>
    // 显示弹窗的函数
    function showQuotePopup(params) {
        // 存储参数到本地存储或全局变量
        localStorage.setItem('quoteParams', params);

        $('#pop_container').removeClass('hidden').addClass('block');
        return false; // 阻止默认行为
    }

    // 统一关闭所有弹窗
    $(document).on('click', '[data-close-modal]', function() {
        $(this).closest('.modal-container').removeClass('block').addClass('hidden');
    });
</script>

<script>
    $('#EmptyMessage').on('click', function() {
        $.ajax({
            url: '/clear-message/',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                console.log(response)
                if (response.code === 1) {
                    // 清空消息内容
                    $('#message_cont ul').empty();
                    // 隐藏查看更多按钮
                    $('#message_more').hide();
                    // 显示无消息提示（移除hidden类）
                    $('#no_message').removeClass('hidden');
                }
            },
            error: function(xhr, status, error) {
                console.error("error:", error);
            }
        });
    });
</script>
