/**
 * CKEditor Mentions 样式文件
 * 用于CKEditor mentions功能的下拉列表和编辑器内mention链接样式
 */

/* Mention 下拉列表样式 */
.cke_autocomplete_panel {
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    max-height: 200px;
    overflow-y: auto;
    z-index: 10000; /* 确保在其他元素之上 */
}

.cke_autocomplete_panel ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.cke_autocomplete_panel li {
    padding: 0;
    margin: 0;
    cursor: pointer;
    border-bottom: 1px solid #f5f5f5;
}

.cke_autocomplete_panel li:last-child {
    border-bottom: none;
}

.cke_autocomplete_panel li:hover,
.cke_autocomplete_panel li.cke_autocomplete_selected {
    background-color: #f0f0f0;
}

.mention-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    gap: 8px;
    transition: background-color 0.2s ease;
}

.mention-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
}

.mention-name {
    font-weight: bold;
    color: #333;
    flex: 1;
}

.mention-username {
    color: #666;
    font-size: 0.9em;
    flex-shrink: 0;
}

/* 编辑器内的 mention 样式 */
.mention {
    background-color: #e3f2fd;
    color: #1976d2;
    padding: 2px 4px;
    border-radius: 3px;
    text-decoration: none;
    font-weight: bold;
    display: inline-block;
    transition: background-color 0.2s ease;
}

.mention:hover {
    background-color: #bbdefb;
    text-decoration: none;
    color: #1565c0;
}

.mention:visited {
    color: #1976d2;
}

.mention:active {
    background-color: #90caf9;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .cke_autocomplete_panel {
        max-height: 150px;
        font-size: 14px;
    }
    
    .mention-item {
        padding: 6px 10px;
        gap: 6px;
    }
    
    .mention-avatar {
        width: 20px;
        height: 20px;
    }
    
    .mention-username {
        font-size: 0.8em;
    }
}

/* 暗色主题支持（可选） */
@media (prefers-color-scheme: dark) {
    .cke_autocomplete_panel {
        background: #2d2d2d;
        border-color: #555;
        color: #fff;
    }
    
    .cke_autocomplete_panel li {
        border-bottom-color: #444;
    }
    
    .cke_autocomplete_panel li:hover,
    .cke_autocomplete_panel li.cke_autocomplete_selected {
        background-color: #404040;
    }
    
    .mention-name {
        color: #fff;
    }
    
    .mention-username {
        color: #ccc;
    }
}
