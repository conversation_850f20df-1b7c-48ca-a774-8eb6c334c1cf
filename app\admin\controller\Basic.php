<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\controller\Common;

use think\facade\Db;

class Basic extends Common
{
    public function index()
    {
        if ($this->request->isPost()) {
            $data = input('post.');
            $id = $data['id'];

            $s = Db::name("Basic")->where("id", $id)->save($data);
            if ($s) {
                //试用券菜单
                Db::name("Backend_menus")->where("id", 16)->update(["status"=>$data['coupon_status']]);

                //论坛系统菜单
                Db::name("Backend_menus")->where("id", 44)->update(["status"=>$data['forum_status']]);

                $this->success('修改成功');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = 1;
            $getone = Db::name("Basic")->where("id", $id)->find();

            return view("", [
                "getone" => $getone,
            ]);
        }
    }

    public function test()
    {
        if ($this->request->isPost()) {
            $data = input('post.');
            $id = $data['id'];

            // 关闭HTML过滤
            $data['content_test1'] = input('post.content_test1', '', null);

            $s = Db::name("Basic")->where("id", $id)->save($data);
            if ($s) {
                $this->success('修改成功');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = 1;
            $getone = Db::name("Basic")->where("id", $id)->find();

            return view("", [
                "getone" => $getone,
            ]);
        }
    }

    //mention实时获取用户列表
    public function searchUsers()
    {
        $query = input('query', '');
        $q = input('q', ''); // 兼容旧版本

        // 优先使用 query 参数，如果没有则使用 q 参数
        $searchTerm = !empty($query) ? $query : $q;

        if (empty($searchTerm)) {
            return json(['code' => 1, 'data' => []]);
        }

        $where = [
            ['first_name|last_name|email|nickname', "like", "%{$searchTerm}%"],
            ['status', "=", 1],  //正常状态
        ];

        $users = Db::name('User')
            ->where($where)
            ->field('id, email, nickname, CONCAT(first_name, " ", last_name) as name, first_name, last_name, avatar')
            ->limit(15)
            ->select()
            ->toArray();

        // 格式化用户数据
        $formattedUsers = [];
        foreach ($users as $user) {
            // 优先使用昵称，其次是姓名，最后是邮箱前缀
            $displayName = '';
            if (!empty($user['nickname'])) {
                $displayName = $user['nickname'];
            } elseif (!empty($user['name']) && trim($user['name']) !== ' ') {
                $displayName = trim($user['name']);
            } else {
                $displayName = explode('@', $user['email'])[0];
            }

            // 用户名使用邮箱前缀或昵称
            $username = !empty($user['nickname']) ? $user['nickname'] : explode('@', $user['email'])[0];

            $formattedUsers[] = [
                'id' => $user['id'],
                'name' => $displayName,
                'username' => $username,
                'avatar' => !empty($user['avatar']) ? $user['avatar'] : '/static/home/<USER>/default-avatar.png'
            ];
        }

        return json(['code' => 1, 'data' => $formattedUsers]);
    }

}
